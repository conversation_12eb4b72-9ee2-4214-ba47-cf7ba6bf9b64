<?php
/**
 * Plugin Name: Rooftops in Barcelona Importer
 * Plugin URI: https://rooftopsbarcelona.local
 * Description: A custom plugin to import and manage rooftop data from JSON files
 * Version: 1.0.0
 * Author: Rooftops in Barcelona
 * Author URI: https://rooftopsbarcelona.local
 * Text Domain: rooftops-in-barcelona-importer
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'RIB_IMPORTER_VERSION', '1.0.0' );
define( 'RIB_IMPORTER_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'RIB_IMPORTER_PLUGIN_URL', plugin_dir_url( __FILE__ ) );

// Include required files
require_once RIB_IMPORTER_PLUGIN_DIR . 'includes/class-rib-post-types.php';
require_once RIB_IMPORTER_PLUGIN_DIR . 'includes/class-rib-taxonomies.php';
require_once RIB_IMPORTER_PLUGIN_DIR . 'includes/class-rib-importer.php';
require_once RIB_IMPORTER_PLUGIN_DIR . 'includes/class-rib-admin.php';

/**
 * Main plugin class
 */
class Rooftops_In_Barcelona_Importer {
    /**
     * Instance of this class
     */
    private static $instance = null;

    /**
     * Post Types instance
     */
    public $post_types;

    /**
     * Taxonomies instance
     */
    public $taxonomies;

    /**
     * Importer instance
     */
    public $importer;

    /**
     * Admin instance
     */
    public $admin;

    /**
     * Get the singleton instance
     */
    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Initialize components
        $this->init_components();

        // Register activation and deactivation hooks
        register_activation_hook( __FILE__, array( $this, 'activate' ) );
        register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );

        // Add actions
        add_action( 'plugins_loaded', array( $this, 'load_textdomain' ) );
    }

    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize post types
        $this->post_types = new RIB_Post_Types();

        // Initialize taxonomies
        $this->taxonomies = new RIB_Taxonomies();

        // Initialize importer
        $this->importer = new RIB_Importer();

        // Initialize admin
        if ( is_admin() ) {
            $this->admin = new RIB_Admin();
        }
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Register post types and taxonomies
        $this->post_types->register_post_types();
        $this->taxonomies->register_taxonomies();

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain( 'rooftops-in-barcelona-importer', false, dirname( plugin_basename( __FILE__ ) ) . '/languages' );
    }
}

// Add JSON as an allowed MIME type
function rib_add_json_mime_type($mime_types) {
    $mime_types['json'] = 'application/json';
    return $mime_types;
}
add_filter('upload_mimes', 'rib_add_json_mime_type');

// Allow JSON files to be uploaded in the media library
function rib_allow_json_upload($data, $file, $filename, $mimes) {
    if (substr($filename, -5) === '.json') {
        $data['ext'] = 'json';
        $data['type'] = 'application/json';
    }
    return $data;
}
add_filter('wp_check_filetype_and_ext', 'rib_allow_json_upload', 10, 4);

// Initialize the plugin
function rooftops_in_barcelona_importer() {
    return Rooftops_In_Barcelona_Importer::get_instance();
}

// Start the plugin
rooftops_in_barcelona_importer();
