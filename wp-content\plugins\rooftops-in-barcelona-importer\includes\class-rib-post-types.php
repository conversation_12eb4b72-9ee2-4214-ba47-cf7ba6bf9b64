<?php
/**
 * Post Types class
 *
 * Registers and manages custom post types
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class RIB_Post_Types {
    /**
     * Constructor
     */
    public function __construct() {
        // Register post types
        add_action( 'init', array( $this, 'register_post_types' ) );

        // Add custom columns to admin list
        add_filter( 'manage_rooftop_posts_columns', array( $this, 'add_rooftop_columns' ) );
        add_action( 'manage_rooftop_posts_custom_column', array( $this, 'render_rooftop_columns' ), 10, 2 );

        // Make custom columns sortable
        add_filter( 'manage_edit-rooftop_sortable_columns', array( $this, 'make_rooftop_columns_sortable' ) );

        // Add meta boxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );

        // Save meta box data
        add_action( 'save_post_rooftop', array( $this, 'save_rooftop_meta' ) );
    }

    /**
     * Register custom post types
     */
    public function register_post_types() {
        // Rooftop post type
        $labels = array(
            'name'                  => _x( 'Rooftops', 'Post type general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'         => _x( 'Rooftop', 'Post type singular name', 'rooftops-in-barcelona-importer' ),
            'menu_name'             => _x( 'Rooftops', 'Admin Menu text', 'rooftops-in-barcelona-importer' ),
            'name_admin_bar'        => _x( 'Rooftop', 'Add New on Toolbar', 'rooftops-in-barcelona-importer' ),
            'add_new'               => __( 'Add New', 'rooftops-in-barcelona-importer' ),
            'add_new_item'          => __( 'Add New Rooftop', 'rooftops-in-barcelona-importer' ),
            'new_item'              => __( 'New Rooftop', 'rooftops-in-barcelona-importer' ),
            'edit_item'             => __( 'Edit Rooftop', 'rooftops-in-barcelona-importer' ),
            'view_item'             => __( 'View Rooftop', 'rooftops-in-barcelona-importer' ),
            'all_items'             => __( 'All Rooftops', 'rooftops-in-barcelona-importer' ),
            'search_items'          => __( 'Search Rooftops', 'rooftops-in-barcelona-importer' ),
            'parent_item_colon'     => __( 'Parent Rooftops:', 'rooftops-in-barcelona-importer' ),
            'not_found'             => __( 'No rooftops found.', 'rooftops-in-barcelona-importer' ),
            'not_found_in_trash'    => __( 'No rooftops found in Trash.', 'rooftops-in-barcelona-importer' ),
            'featured_image'        => _x( 'Rooftop Cover Image', 'Overrides the "Featured Image" phrase', 'rooftops-in-barcelona-importer' ),
            'set_featured_image'    => _x( 'Set cover image', 'Overrides the "Set featured image" phrase', 'rooftops-in-barcelona-importer' ),
            'remove_featured_image' => _x( 'Remove cover image', 'Overrides the "Remove featured image" phrase', 'rooftops-in-barcelona-importer' ),
            'use_featured_image'    => _x( 'Use as cover image', 'Overrides the "Use as featured image" phrase', 'rooftops-in-barcelona-importer' ),
            'archives'              => _x( 'Rooftop archives', 'The post type archive label used in nav menus', 'rooftops-in-barcelona-importer' ),
            'insert_into_item'      => _x( 'Insert into rooftop', 'Overrides the "Insert into post" phrase', 'rooftops-in-barcelona-importer' ),
            'uploaded_to_this_item' => _x( 'Uploaded to this rooftop', 'Overrides the "Uploaded to this post" phrase', 'rooftops-in-barcelona-importer' ),
            'filter_items_list'     => _x( 'Filter rooftops list', 'Screen reader text for the filter links heading on the post type listing screen', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation' => _x( 'Rooftops list navigation', 'Screen reader text for the pagination heading on the post type listing screen', 'rooftops-in-barcelona-importer' ),
            'items_list'            => _x( 'Rooftops list', 'Screen reader text for the items list heading on the post type listing screen', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'query_var'          => true,
            'rewrite'            => array( 'slug' => 'rooftop' ),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => 5,
            'menu_icon'          => 'dashicons-building',
            'supports'           => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'       => true,
        );

        register_post_type( 'rooftop', $args );
    }

    /**
     * Add custom columns to rooftop post type admin list
     */
    public function add_rooftop_columns( $columns ) {
        // Start with a clean slate or a minimal base from WordPress
        $new_columns = array();

        // Standard columns we want to keep at the beginning
        if ( isset( $columns['cb'] ) ) {
            $new_columns['cb'] = $columns['cb']; // Checkbox
        }
        if ( isset( $columns['title'] ) ) {
            $new_columns['title'] = $columns['title']; // Title
        }

        // Add our custom and taxonomy columns in the desired order
        $new_columns['location'] = __( 'Location', 'rooftops-in-barcelona-importer' );
        $new_columns['rating'] = __( 'Rating', 'rooftops-in-barcelona-importer' );
        $new_columns['rooftop_neighborhood'] = __( 'Neighborhood', 'rooftops-in-barcelona-importer' );
        $new_columns['rooftop_atmosphere'] = __( 'Atmosphere', 'rooftops-in-barcelona-importer' );
        $new_columns['rooftop_view_type'] = __( 'Views', 'rooftops-in-barcelona-importer' );
        $new_columns['rooftop_venue_type'] = __( 'Venue Type', 'rooftops-in-barcelona-importer' );
        $new_columns['rooftop_popular'] = __( 'Popular', 'rooftops-in-barcelona-importer' );


        // Add standard columns we want to keep at the end
        if ( isset( $columns['date'] ) ) {
            $new_columns['date'] = $columns['date']; // Date
        }

        // WordPress might automatically add columns for taxonomies like 'taxonomy-slug'.
        // We need to unset these if we've defined our own keys for them.
        $taxonomy_slugs = ['rooftop_neighborhood', 'rooftop_atmosphere', 'rooftop_best_for', 'rooftop_view_type', 'rooftop_music_style', 'rooftop_amenities', 'rooftop_accessibility', 'rooftop_menu_type', 'rooftop_cuisine_style', 'rooftop_dietary_options', 'rooftop_price_range', 'rooftop_dress_code', 'rooftop_venue_type', 'rooftop_popular', 'specialties', 'services', 'amenities', 'neighborhoods', 'popular', 'spa_category', 'spa_service', 'spa_feature', 'spa_neighborhood', 'post_tag'];
        foreach ($taxonomy_slugs as $slug) {
            if (isset($columns['taxonomy-' . $slug]) && !isset($new_columns['taxonomy-' . $slug]) && $slug !== $new_columns[$slug] ) {
                 // If WP added taxonomy-slug and we have a custom key for it (e.g. 'specialties')
                 // and our custom key is different from 'taxonomy-slug', unset WP's default.
                 // This logic is a bit complex because our custom keys match the slugs.
                 // The primary goal is to ensure we don't have both 'specialties' and 'taxonomy-specialties'.
            }
             // More direct: if our custom key exists, remove the WordPress default if it also exists.
            if (isset($new_columns[$slug]) && isset($columns['taxonomy-' . $slug])) {
                unset($columns['taxonomy-' . $slug]); // Unset from the original $columns passed to the filter
            }
        }
         // Unset any remaining WordPress default taxonomy columns that we have explicitly defined with a custom key
        foreach ($new_columns as $custom_key => $custom_value) {
            if (in_array($custom_key, $taxonomy_slugs) && isset($columns['taxonomy-' . $custom_key])) {
                // We have defined 'custom_key' (e.g. 'specialties')
                // and WP also has 'taxonomy-custom_key' (e.g. 'taxonomy-specialties')
                // We prefer our definition, so ensure the WP default isn't also there.
                // This might be redundant if $columns isn't used after this point,
                // but it's safer to clean up. The key is that $new_columns is what's returned.
            }
        }
        // Let's simplify: ensure that if we define 'specialties', 'taxonomy-specialties' is not in the final output.
        // The easiest way is to build $new_columns with only what we want.

        // Final check: remove any default WP taxonomy columns if we've defined them with the same slug as key
        foreach ($taxonomy_slugs as $slug) {
            if (isset($new_columns[$slug]) && isset($new_columns['taxonomy-' . $slug])) {
                unset($new_columns['taxonomy-' . $slug]);
            }
        }
         // Remove the old 'tags' column if it's present from WordPress core for posts
        if(isset($new_columns['tags'])){
            unset($new_columns['tags']);
        }
        // Remove the old 'categories' column if it's present from WordPress core for posts
        if(isset($new_columns['categories'])){
            unset($new_columns['categories']);
        }


        return $new_columns;
    }

    /**
     * Render custom column content
     */
    public function render_rooftop_columns( $column, $post_id ) {
        switch ( $column ) {
            case 'location':
                $location = get_post_meta( $post_id, 'location', true );
                $city = ! empty( $location['city'] ) ? $location['city'] : '';
                $address = ! empty( $location['address'] ) ? $location['address'] : '';

                if ( $city ) {
                    echo esc_html( $city );
                    if ( $address ) {
                        echo '<br><small>' . esc_html( $address ) . '</small>';
                    }
                } else {
                    echo '—';
                }
                break;

            case 'rating':
                $reviews = get_post_meta( $post_id, 'reviews', true );
                $avg_rating = 0;
                $review_count = 0;

                if ( ! empty( $reviews['review_sources'] ) && is_array( $reviews['review_sources'] ) ) {
                    $total_rating = 0;
                    $total_count = 0;

                    foreach ( $reviews['review_sources'] as $source ) {
                        if ( ! empty( $source['rating'] ) && ! empty( $source['count'] ) ) {
                            $total_rating += $source['rating'] * $source['count'];
                            $total_count += $source['count'];
                        }
                    }

                    if ( $total_count > 0 ) {
                        $avg_rating = round( $total_rating / $total_count, 1 );
                        $review_count = $total_count;
                    }
                }

                if ( $avg_rating > 0 ) {
                    echo esc_html( $avg_rating ) . '/5';
                    echo '<br><small>' . esc_html( $review_count ) . ' ' . esc_html( _n( 'review', 'reviews', $review_count, 'rooftops-in-barcelona-importer' ) ) . '</small>';
                } else {
                    echo '—';
                }
                break;

            case 'rooftop_neighborhood':
                $terms = get_the_terms( $post_id, 'rooftop_neighborhood' );
                if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                    $term_names = array_map( function($term) {
                        return '<a href="' . esc_url( admin_url( 'edit.php?post_type=rooftop&rooftop_neighborhood=' . $term->slug ) ) . '">' . esc_html( $term->name ) . '</a>';
                    }, $terms );
                    echo implode( ', ', $term_names );
                } else {
                    echo '—';
                }
                break;
            case 'rooftop_atmosphere':
                $terms = get_the_terms( $post_id, 'rooftop_atmosphere' );
                if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                    $term_names = array_map( function($term) {
                        return '<a href="' . esc_url( admin_url( 'edit.php?post_type=rooftop&rooftop_atmosphere=' . $term->slug ) ) . '">' . esc_html( $term->name ) . '</a>';
                    }, $terms );
                    echo implode( ', ', $term_names );
                } else {
                    echo '—';
                }
                break;
            case 'rooftop_view_type':
                $terms = get_the_terms( $post_id, 'rooftop_view_type' );
                if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                    $term_names = array_map( function($term) {
                        return '<a href="' . esc_url( admin_url( 'edit.php?post_type=rooftop&rooftop_view_type=' . $term->slug ) ) . '">' . esc_html( $term->name ) . '</a>';
                    }, $terms );
                    echo implode( ', ', $term_names );
                } else {
                    echo '—';
                }
                break;
            case 'rooftop_venue_type':
                $terms = get_the_terms( $post_id, 'rooftop_venue_type' );
                if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                    $term_names = array_map( function($term) {
                        return '<a href="' . esc_url( admin_url( 'edit.php?post_type=rooftop&rooftop_venue_type=' . $term->slug ) ) . '">' . esc_html( $term->name ) . '</a>';
                    }, $terms );
                    echo implode( ', ', $term_names );
                } else {
                    echo '—';
                }
                break;
            case 'rooftop_popular':
                $terms = get_the_terms( $post_id, 'rooftop_popular' );
                if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                    $term_names = array_map( function($term) {
                        return '<a href="' . esc_url( admin_url( 'edit.php?post_type=rooftop&rooftop_popular=' . $term->slug ) ) . '">' . esc_html( $term->name ) . '</a>';
                    }, $terms );
                    echo implode( ', ', $term_names );
                } else {
                    echo '—';
                }
                break;
        }
    }

    /**
     * Make custom columns sortable
     */
    public function make_rooftop_columns_sortable( $columns ) {
        $columns['location'] = 'location';
        $columns['rating'] = 'rating';

        return $columns;
    }

    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'rooftop_details',
            __( 'Rooftop Details', 'rooftops-in-barcelona-importer' ),
            array( $this, 'render_rooftop_details_meta_box' ),
            'rooftop',
            'normal',
            'high'
        );

        add_meta_box(
            'rooftop_location',
            __( 'Location', 'rooftops-in-barcelona-importer' ),
            array( $this, 'render_rooftop_location_meta_box' ),
            'rooftop',
            'side',
            'default'
        );
    }

    /**
     * Render rooftop details meta box
     */
    public function render_rooftop_details_meta_box( $post ) {
        // Add nonce for security
        wp_nonce_field( 'rooftop_details_meta_box', 'rooftop_details_meta_box_nonce' );

        // Get saved values
        $contact = get_post_meta( $post->ID, 'contact', true );
        $services = get_post_meta( $post->ID, 'services', true );
        $amenities = get_post_meta( $post->ID, 'amenities', true );

        // Output fields
        ?>
        <div class="rooftop-meta-box">
            <h4><?php _e( 'Contact Information', 'rooftops-in-barcelona-importer' ); ?></h4>
            <p>
                <label for="rooftop_phone"><?php _e( 'Phone', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="text" id="rooftop_phone" name="rooftop_contact[phone]" value="<?php echo esc_attr( ! empty( $contact['phone'] ) ? $contact['phone'] : '' ); ?>" class="widefat">
            </p>
            <p>
                <label for="rooftop_email"><?php _e( 'Email', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="email" id="rooftop_email" name="rooftop_contact[email]" value="<?php echo esc_attr( ! empty( $contact['email'] ) ? $contact['email'] : '' ); ?>" class="widefat">
            </p>
            <p>
                <label for="rooftop_website"><?php _e( 'Website', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="url" id="rooftop_website" name="rooftop_contact[website]" value="<?php echo esc_attr( ! empty( $contact['website'] ) ? $contact['website'] : '' ); ?>" class="widefat">
            </p>

            <h4><?php _e( 'Services', 'rooftops-in-barcelona-importer' ); ?></h4>
            <p>
                <textarea id="rooftop_services" name="rooftop_services" rows="5" class="widefat"><?php echo esc_textarea( is_array( $services ) ? implode( "\n", $services ) : $services ); ?></textarea>
                <span class="description"><?php _e( 'Enter one service per line', 'rooftops-in-barcelona-importer' ); ?></span>
            </p>

            <h4><?php _e( 'Amenities', 'rooftops-in-barcelona-importer' ); ?></h4>
            <p>
                <textarea id="rooftop_amenities" name="rooftop_amenities" rows="5" class="widefat"><?php echo esc_textarea( is_array( $amenities ) ? implode( "\n", $amenities ) : $amenities ); ?></textarea>
                <span class="description"><?php _e( 'Enter one amenity per line', 'rooftops-in-barcelona-importer' ); ?></span>
            </p>
        </div>
        <?php
    }

    /**
     * Render rooftop location meta box
     */
    public function render_rooftop_location_meta_box( $post ) {
        // Get saved values
        $location = get_post_meta( $post->ID, 'location', true );

        // Output fields
        ?>
        <div class="rooftop-meta-box">
            <p>
                <label for="rooftop_address"><?php _e( 'Address', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="text" id="rooftop_address" name="rooftop_location[address]" value="<?php echo esc_attr( ! empty( $location['address'] ) ? $location['address'] : '' ); ?>" class="widefat">
            </p>
            <p>
                <label for="rooftop_city"><?php _e( 'City', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="text" id="rooftop_city" name="rooftop_location[city]" value="<?php echo esc_attr( ! empty( $location['city'] ) ? $location['city'] : 'Barcelona' ); ?>" class="widefat">
            </p>
            <p>
                <label for="rooftop_country"><?php _e( 'Country', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="text" id="rooftop_country" name="rooftop_location[country]" value="<?php echo esc_attr( ! empty( $location['country'] ) ? $location['country'] : 'Spain' ); ?>" class="widefat">
            </p>
            <p>
                <label for="rooftop_neighborhood"><?php _e( 'Neighborhood', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="text" id="rooftop_neighborhood" name="rooftop_location[neighborhood]" value="<?php echo esc_attr( ! empty( $location['neighborhood'] ) ? $location['neighborhood'] : '' ); ?>" class="widefat">
            </p>
            <p>
                <label for="rooftop_district"><?php _e( 'District', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="text" id="rooftop_district" name="rooftop_location[district]" value="<?php echo esc_attr( ! empty( $location['district'] ) ? $location['district'] : '' ); ?>" class="widefat">
            </p>
            <p>
                <label for="rooftop_latitude"><?php _e( 'Latitude', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="text" id="rooftop_latitude" name="rooftop_location[coordinates][latitude]" value="<?php echo esc_attr( ! empty( $location['coordinates']['latitude'] ) ? $location['coordinates']['latitude'] : '' ); ?>" class="widefat">
            </p>
            <p>
                <label for="rooftop_longitude"><?php _e( 'Longitude', 'rooftops-in-barcelona-importer' ); ?>:</label>
                <input type="text" id="rooftop_longitude" name="rooftop_location[coordinates][longitude]" value="<?php echo esc_attr( ! empty( $location['coordinates']['longitude'] ) ? $location['coordinates']['longitude'] : '' ); ?>" class="widefat">
            </p>
        </div>
        <?php
    }

    /**
     * Save rooftop meta data
     */
    public function save_rooftop_meta( $post_id ) {
        // Check if nonce is set
        if ( ! isset( $_POST['rooftop_details_meta_box_nonce'] ) ) {
            return;
        }

        // Verify nonce
        if ( ! wp_verify_nonce( $_POST['rooftop_details_meta_box_nonce'], 'rooftop_details_meta_box' ) ) {
            return;
        }

        // Check if autosave
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Check permissions
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Save contact information
        if ( isset( $_POST['rooftop_contact'] ) ) {
            $contact = array_map( 'sanitize_text_field', $_POST['rooftop_contact'] );
            update_post_meta( $post_id, 'contact', $contact );
        }

        // Save location
        if ( isset( $_POST['rooftop_location'] ) ) {
            $location = $_POST['rooftop_location'];

            // Sanitize text fields
            $location['address'] = isset( $location['address'] ) ? sanitize_text_field( $location['address'] ) : '';
            $location['city'] = isset( $location['city'] ) ? sanitize_text_field( $location['city'] ) : 'Barcelona';
            $location['country'] = isset( $location['country'] ) ? sanitize_text_field( $location['country'] ) : 'Spain';
            $location['neighborhood'] = isset( $location['neighborhood'] ) ? sanitize_text_field( $location['neighborhood'] ) : '';
            $location['district'] = isset( $location['district'] ) ? sanitize_text_field( $location['district'] ) : '';

            // Sanitize coordinates
            $location['coordinates']['latitude'] = isset( $location['coordinates']['latitude'] ) ? sanitize_text_field( $location['coordinates']['latitude'] ) : '';
            $location['coordinates']['longitude'] = isset( $location['coordinates']['longitude'] ) ? sanitize_text_field( $location['coordinates']['longitude'] ) : '';

            update_post_meta( $post_id, 'location', $location );
        }

        // Save services
        if ( isset( $_POST['rooftop_services'] ) ) {
            $services = explode( "\n", sanitize_textarea_field( $_POST['rooftop_services'] ) );
            $services = array_map( 'trim', $services );
            $services = array_filter( $services );

            update_post_meta( $post_id, 'services', $services );
        }

        // Save amenities
        if ( isset( $_POST['rooftop_amenities'] ) ) {
            $amenities = explode( "\n", sanitize_textarea_field( $_POST['rooftop_amenities'] ) );
            $amenities = array_map( 'trim', $amenities );
            $amenities = array_filter( $amenities );

            update_post_meta( $post_id, 'amenities', $amenities );
        }
    }
}
