<?php
/**
 * Taxonomies class
 *
 * Registers and manages custom taxonomies
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class RIB_Taxonomies {
    /**
     * Constructor
     */
    public function __construct() {
        // Register taxonomies
        add_action( 'init', array( $this, 'register_taxonomies' ) );

        // Define rooftop taxonomies
        $rooftop_taxonomies = [
            'rooftop_neighborhood', 'rooftop_atmosphere', 'rooftop_best_for', 'rooftop_view_type',
            'rooftop_music_style', 'rooftop_amenities', 'rooftop_accessibility', 'rooftop_menu_type',
            'rooftop_cuisine_style', 'rooftop_dietary_options', 'rooftop_price_range', 'rooftop_dress_code',
            'rooftop_venue_type', 'rooftop_popular'
        ];

        // Add custom columns to taxonomy admin list for all rooftop taxonomies
        foreach ( $rooftop_taxonomies as $taxonomy ) {
            add_filter( "manage_edit-{$taxonomy}_columns", array( $this, 'add_taxonomy_columns' ) );
            add_filter( "manage_{$taxonomy}_custom_column", array( $this, 'render_taxonomy_columns' ), 10, 3 );

            // Add term meta fields
            add_action( "{$taxonomy}_add_form_fields", array( $this, 'add_term_meta_fields' ) );
            add_action( "{$taxonomy}_edit_form_fields", array( $this, 'edit_term_meta_fields' ), 10, 2 );

            // Save term meta
            add_action( "created_{$taxonomy}", array( $this, 'save_term_meta' ), 10, 2 );
            add_action( "edited_{$taxonomy}", array( $this, 'save_term_meta' ), 10, 2 );
        }
    }

    /**
     * Register custom taxonomies
     */
    public function register_taxonomies() {
        // 1. Rooftop Neighborhood taxonomy (hierarchical)
        $labels = array(
            'name'                       => _x( 'Neighborhoods', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Neighborhood', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Neighborhoods', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Neighborhoods', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Neighborhoods', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => __( 'Parent Neighborhood', 'rooftops-in-barcelona-importer' ),
            'parent_item_colon'          => __( 'Parent Neighborhood:', 'rooftops-in-barcelona-importer' ),
            'edit_item'                  => __( 'Edit Neighborhood', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Neighborhood', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Neighborhood', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Neighborhood', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Neighborhood Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate neighborhoods with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove neighborhoods', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used neighborhoods', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No neighborhoods found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No neighborhoods', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Neighborhoods', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Neighborhoods list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Neighborhoods list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Neighborhoods', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => true,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-neighborhoods' ),
        );

        register_taxonomy( 'rooftop_neighborhood', 'rooftop', $args );

        // 2. Rooftop Atmosphere taxonomy
        $labels = array(
            'name'                       => _x( 'Atmosphere', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Atmosphere', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Atmosphere', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Atmosphere', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Atmosphere', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Atmosphere', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Atmosphere', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Atmosphere', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Atmosphere', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Atmosphere Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate atmosphere with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove atmosphere', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used atmosphere', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No atmosphere found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No atmosphere', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Atmosphere', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Atmosphere list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Atmosphere list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Atmosphere', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-atmosphere' ),
        );

        register_taxonomy( 'rooftop_atmosphere', 'rooftop', $args );

        // 3. Rooftop Best For taxonomy
        $labels = array(
            'name'                       => _x( 'Best For', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Best For', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Best For', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Best For', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Best For', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Best For', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Best For', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Best For', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Best For', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Best For Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate best for with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove best for', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used best for', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No best for found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No best for', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Best For', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Best For list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Best For list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Best For', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-best-for' ),
        );

        register_taxonomy( 'rooftop_best_for', 'rooftop', $args );

        // 4. Rooftop View Type taxonomy
        $labels = array(
            'name'                       => _x( 'View Types', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'View Type', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search View Types', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular View Types', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All View Types', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit View Type', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View View Type', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update View Type', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New View Type', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New View Type Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate view types with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove view types', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used view types', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No view types found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No view types', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'View Types', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'View Types list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'View Types list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to View Types', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-view-types' ),
        );

        register_taxonomy( 'rooftop_view_type', 'rooftop', $args );

        // 5. Rooftop Music Style taxonomy
        $labels = array(
            'name'                       => _x( 'Music Styles', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Music Style', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Music Styles', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Music Styles', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Music Styles', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Music Style', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Music Style', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Music Style', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Music Style', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Music Style Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate music styles with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove music styles', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used music styles', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No music styles found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No music styles', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Music Styles', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Music Styles list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Music Styles list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Music Styles', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-music-styles' ),
        );

        register_taxonomy( 'rooftop_music_style', 'rooftop', $args );

        // 6. Rooftop Amenities taxonomy
        $labels = array(
            'name'                       => _x( 'Amenities', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Amenity', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Amenities', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Amenities', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Amenities', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Amenity', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Amenity', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Amenity', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Amenity', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Amenity Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate amenities with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove amenities', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used amenities', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No amenities found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No amenities', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Amenities', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Amenities list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Amenities list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Amenities', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-amenities' ),
        );

        register_taxonomy( 'rooftop_amenities', 'rooftop', $args );

        // 7. Rooftop Accessibility taxonomy
        $labels = array(
            'name'                       => _x( 'Accessibility', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Accessibility', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Accessibility', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Accessibility', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Accessibility', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Accessibility', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Accessibility', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Accessibility', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Accessibility', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Accessibility Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate accessibility with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove accessibility', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used accessibility', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No accessibility found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No accessibility', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Accessibility', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Accessibility list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Accessibility list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Accessibility', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-accessibility' ),
        );

        register_taxonomy( 'rooftop_accessibility', 'rooftop', $args );

        // 8. Rooftop Menu Type taxonomy
        $labels = array(
            'name'                       => _x( 'Menu Types', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Menu Type', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Menu Types', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Menu Types', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Menu Types', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Menu Type', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Menu Type', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Menu Type', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Menu Type', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Menu Type Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate menu types with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove menu types', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used menu types', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No menu types found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No menu types', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Menu Types', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Menu Types list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Menu Types list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Menu Types', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-menu-types' ),
        );

        register_taxonomy( 'rooftop_menu_type', 'rooftop', $args );

        // 9. Rooftop Cuisine Style taxonomy
        $labels = array(
            'name'                       => _x( 'Cuisine Styles', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Cuisine Style', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Cuisine Styles', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Cuisine Styles', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Cuisine Styles', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Cuisine Style', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Cuisine Style', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Cuisine Style', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Cuisine Style', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Cuisine Style Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate cuisine styles with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove cuisine styles', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used cuisine styles', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No cuisine styles found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No cuisine styles', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Cuisine Styles', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Cuisine Styles list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Cuisine Styles list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Cuisine Styles', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-cuisine-styles' ),
        );

        register_taxonomy( 'rooftop_cuisine_style', 'rooftop', $args );

        // 10. Rooftop Dietary Options taxonomy
        $labels = array(
            'name'                       => _x( 'Dietary Options', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Dietary Option', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Dietary Options', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Dietary Options', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Dietary Options', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Dietary Option', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Dietary Option', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Dietary Option', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Dietary Option', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Dietary Option Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate dietary options with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove dietary options', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used dietary options', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No dietary options found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No dietary options', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Dietary Options', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Dietary Options list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Dietary Options list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Dietary Options', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-dietary-options' ),
        );

        register_taxonomy( 'rooftop_dietary_options', 'rooftop', $args );

        // 11. Rooftop Price Range taxonomy
        $labels = array(
            'name'                       => _x( 'Price Ranges', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Price Range', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Price Ranges', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Price Ranges', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Price Ranges', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Price Range', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Price Range', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Price Range', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Price Range', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Price Range Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate price ranges with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove price ranges', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used price ranges', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No price ranges found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No price ranges', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Price Ranges', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Price Ranges list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Price Ranges list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Price Ranges', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-price-ranges' ),
        );

        register_taxonomy( 'rooftop_price_range', 'rooftop', $args );

        // 12. Rooftop Dress Code taxonomy
        $labels = array(
            'name'                       => _x( 'Dress Codes', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Dress Code', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Dress Codes', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Dress Codes', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Dress Codes', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Dress Code', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Dress Code', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Dress Code', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Dress Code', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Dress Code Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate dress codes with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove dress codes', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used dress codes', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No dress codes found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No dress codes', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Dress Codes', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Dress Codes list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Dress Codes list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Dress Codes', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-dress-codes' ),
        );

        register_taxonomy( 'rooftop_dress_code', 'rooftop', $args );

        // 13. Rooftop Venue Type taxonomy
        $labels = array(
            'name'                       => _x( 'Venue Types', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Venue Type', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Venue Types', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Venue Types', 'rooftops-in-barcelona-importer' ),
            'all_items'                  => __( 'All Venue Types', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Venue Type', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Venue Type', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Venue Type', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Venue Type', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Venue Type Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate venue types with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove venue types', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used venue types', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No venue types found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No venue types', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Venue Types', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Venue Types list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Venue Types list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Venue Types', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-venue-types' ),
        );

        register_taxonomy( 'rooftop_venue_type', 'rooftop', $args );

        // 14. Rooftop Popular taxonomy (replaces the old popular taxonomy)
        $labels = array(
            'name'                       => _x( 'Popular', 'Taxonomy general name', 'rooftops-in-barcelona-importer' ),
            'singular_name'              => _x( 'Popular Item', 'Taxonomy singular name', 'rooftops-in-barcelona-importer' ),
            'search_items'               => __( 'Search Popular Items', 'rooftops-in-barcelona-importer' ),
            'popular_items'              => null,
            'all_items'                  => __( 'All Popular Items', 'rooftops-in-barcelona-importer' ),
            'parent_item'                => null,
            'parent_item_colon'          => null,
            'edit_item'                  => __( 'Edit Popular Item', 'rooftops-in-barcelona-importer' ),
            'view_item'                  => __( 'View Popular Item', 'rooftops-in-barcelona-importer' ),
            'update_item'                => __( 'Update Popular Item', 'rooftops-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Popular Item', 'rooftops-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Popular Item Name', 'rooftops-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate popular items with commas', 'rooftops-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove popular items', 'rooftops-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used popular items', 'rooftops-in-barcelona-importer' ),
            'not_found'                  => __( 'No popular items found.', 'rooftops-in-barcelona-importer' ),
            'no_terms'                   => __( 'No popular items', 'rooftops-in-barcelona-importer' ),
            'menu_name'                  => __( 'Popular', 'rooftops-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Popular items list navigation', 'rooftops-in-barcelona-importer' ),
            'items_list'                 => __( 'Popular items list', 'rooftops-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Popular Items', 'rooftops-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-popular' ),
        );

        register_taxonomy( 'rooftop_popular', 'rooftop', $args );

        // Flush rewrite rules to ensure the new taxonomy URLs work
        // Only flush once to avoid performance issues
        // Note: Consider a more robust flushing mechanism if multiple taxonomies are frequently changed.
        if ( ! get_option( 'sib_rooftop_taxonomies_flushed_v1' ) || isset($_GET['flush_rules']) ) {
            // Log before flushing
            error_log('About to flush rewrite rules for rooftop taxonomies');

            // Force update taxonomy slugs before flushing
            global $wp_taxonomies;
            $rooftop_taxonomies = [
                'rooftop_neighborhood' => 'rooftop-neighborhoods',
                'rooftop_atmosphere' => 'rooftop-atmosphere',
                'rooftop_best_for' => 'rooftop-best-for',
                'rooftop_view_type' => 'rooftop-view-types',
                'rooftop_music_style' => 'rooftop-music-styles',
                'rooftop_amenities' => 'rooftop-amenities',
                'rooftop_accessibility' => 'rooftop-accessibility',
                'rooftop_menu_type' => 'rooftop-menu-types',
                'rooftop_cuisine_style' => 'rooftop-cuisine-styles',
                'rooftop_dietary_options' => 'rooftop-dietary-options',
                'rooftop_price_range' => 'rooftop-price-ranges',
                'rooftop_dress_code' => 'rooftop-dress-codes',
                'rooftop_venue_type' => 'rooftop-venue-types',
                'rooftop_popular' => 'rooftop-popular'
            ];

            foreach ( $rooftop_taxonomies as $taxonomy => $slug ) {
                if ( isset( $wp_taxonomies[$taxonomy] ) ) {
                    $wp_taxonomies[$taxonomy]->rewrite['slug'] = $slug;
                    error_log("Updated {$taxonomy} taxonomy rewrite rules to {$slug}");
                }
            }


            // Flush rewrite rules
            flush_rewrite_rules();
            update_option( 'sib_rooftop_taxonomies_flushed_v1', true );

            // Log the action
            if (isset($_GET['flush_rules'])) {
                error_log('Rewrite rules flushed manually from rooftop taxonomy registration');
            } else {
                error_log('Rewrite rules flushed automatically from rooftop taxonomy registration');
            }

            // Redirect to prevent multiple flushes if this was triggered manually
            if (isset($_GET['flush_rules']) && !is_admin()) {
                // Remove the query parameter and redirect
                $redirect_url = remove_query_arg('flush_rules');
                if (!empty($redirect_url)) {
                    wp_redirect($redirect_url);
                    exit;
                }
            }
        }
    }

    /**
     * Add custom columns to taxonomy admin list
     */
    public function add_taxonomy_columns( $columns ) {
        $new_columns = array();

        foreach ( $columns as $key => $value ) {
            $new_columns[ $key ] = $value;

            if ( $key === 'name' ) {
                $new_columns['icon'] = __( 'Icon', 'rooftops-in-barcelona-importer' );
                $new_columns['description'] = __( 'Description', 'rooftops-in-barcelona-importer' );
            }
        }

        return $new_columns;
    }

    /**
     * Render custom column content
     */
    public function render_taxonomy_columns( $content, $column_name, $term_id ) {
        switch ( $column_name ) {
            case 'icon':
                $icon = get_term_meta( $term_id, 'icon', true );
                if ( $icon ) {
                    $content = '<i class="fas fa-' . esc_attr( $icon ) . '"></i> ' . esc_html( $icon );
                } else {
                    $content = '—';
                }
                break;

            case 'description':
                $term = get_term( $term_id );
                $content = ! empty( $term->description ) ? esc_html( wp_trim_words( $term->description, 10 ) ) : '—';
                break;
        }

        return $content;
    }

    /**
     * Add term meta fields to add form
     */
    public function add_term_meta_fields() {
        ?>
        <div class="form-field">
            <label for="term_meta_icon"><?php _e( 'Icon', 'rooftops-in-barcelona-importer' ); ?></label>
            <input type="text" name="term_meta[icon]" id="term_meta_icon" value="">
            <p class="description"><?php _e( 'Enter a Font Awesome icon name (e.g., "spa" for fa-spa)', 'rooftops-in-barcelona-importer' ); ?></p>
        </div>

        <div class="form-field">
            <label for="term_meta_seo_title"><?php _e( 'SEO Title', 'rooftops-in-barcelona-importer' ); ?></label>
            <input type="text" name="term_meta[seo_title]" id="term_meta_seo_title" value="">
            <p class="description"><?php _e( 'Custom title tag for this term archive page', 'rooftops-in-barcelona-importer' ); ?></p>
        </div>

        <div class="form-field">
            <label for="term_meta_seo_description"><?php _e( 'SEO Description', 'rooftops-in-barcelona-importer' ); ?></label>
            <textarea name="term_meta[seo_description]" id="term_meta_seo_description" rows="5"></textarea>
            <p class="description"><?php _e( 'Custom meta description for this term archive page', 'rooftops-in-barcelona-importer' ); ?></p>
        </div>
        <?php
    }

    /**
     * Add term meta fields to edit form
     */
    public function edit_term_meta_fields( $term, $taxonomy ) {
        // Get term meta
        $term_meta = get_term_meta( $term->term_id );
        $icon = isset( $term_meta['icon'][0] ) ? $term_meta['icon'][0] : '';
        $seo_title = isset( $term_meta['seo_title'][0] ) ? $term_meta['seo_title'][0] : '';
        $seo_description = isset( $term_meta['seo_description'][0] ) ? $term_meta['seo_description'][0] : '';
        ?>
        <tr class="form-field">
            <th scope="row" valign="top">
                <label for="term_meta_icon"><?php _e( 'Icon', 'rooftops-in-barcelona-importer' ); ?></label>
            </th>
            <td>
                <input type="text" name="term_meta[icon]" id="term_meta_icon" value="<?php echo esc_attr( $icon ); ?>">
                <p class="description"><?php _e( 'Enter a Font Awesome icon name (e.g., "spa" for fa-spa)', 'rooftops-in-barcelona-importer' ); ?></p>
                <?php if ( $icon ) : ?>
                    <p><i class="fas fa-<?php echo esc_attr( $icon ); ?>"></i> <?php echo esc_html( $icon ); ?></p>
                <?php endif; ?>
            </td>
        </tr>

        <tr class="form-field">
            <th scope="row" valign="top">
                <label for="term_meta_seo_title"><?php _e( 'SEO Title', 'rooftops-in-barcelona-importer' ); ?></label>
            </th>
            <td>
                <input type="text" name="term_meta[seo_title]" id="term_meta_seo_title" value="<?php echo esc_attr( $seo_title ); ?>">
                <p class="description"><?php _e( 'Custom title tag for this term archive page', 'rooftops-in-barcelona-importer' ); ?></p>
            </td>
        </tr>

        <tr class="form-field">
            <th scope="row" valign="top">
                <label for="term_meta_seo_description"><?php _e( 'SEO Description', 'rooftops-in-barcelona-importer' ); ?></label>
            </th>
            <td>
                <textarea name="term_meta[seo_description]" id="term_meta_seo_description" rows="5"><?php echo esc_textarea( $seo_description ); ?></textarea>
                <p class="description"><?php _e( 'Custom meta description for this term archive page', 'rooftops-in-barcelona-importer' ); ?></p>
            </td>
        </tr>
        <?php
    }

    /**
     * Save term meta
     */
    public function save_term_meta( $term_id, $tt_id ) {
        if ( isset( $_POST['term_meta'] ) ) {
            $term_meta = $_POST['term_meta'];

            foreach ( $term_meta as $key => $value ) {
                if ( $key === 'seo_description' ) {
                    $value = sanitize_textarea_field( $value );
                } else {
                    $value = sanitize_text_field( $value );
                }

                update_term_meta( $term_id, $key, $value );
            }
        }
    }
}
