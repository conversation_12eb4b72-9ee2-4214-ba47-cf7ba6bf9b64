/**
 * Admin scripts for Spas in Barcelona Importer
 */
(function($) {
    'use strict';

    // Import page functionality
    $(document).ready(function() {
        // Tab navigation
        $('.sib-tab-link').on('click', function() {
            var tabId = $(this).data('tab');

            // Update active tab
            $('.sib-tab-link').removeClass('active');
            $(this).addClass('active');

            // Show selected tab content
            $('.sib-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        });

        // Help tab navigation
        $('.sib-help-tab-link').on('click', function() {
            var tabId = $(this).data('tab');

            // Update active tab
            $('.sib-help-tab-link').removeClass('active');
            $(this).addClass('active');

            // Show selected tab content
            $('.sib-help-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        });

        // Method tab navigation
        $('.sib-method-tab').on('click', function() {
            var methodId = $(this).data('method');

            // Update active tab
            $('.sib-method-tab').removeClass('active');
            $(this).addClass('active');

            // Show selected method content
            $('.sib-method-content').removeClass('active');
            $('#' + methodId + '-method').addClass('active');
        });

        // File selection for single import
        $('#select-json-file').on('click', function(e) {
            e.preventDefault();

            var fileFrame = wp.media({
                title: sibAdmin.i18n.selectFile,
                button: {
                    text: sibAdmin.i18n.selectFile
                },
                multiple: false,
                library: {
                    type: ['application/json', 'text/plain']  // Accept both JSON and text MIME types
                },
                uploader: {
                    allowedTypes: ['json']  // Only allow .json extension
                }
            });

            // Handle upload errors
            fileFrame.on('uploader:error', function(error) {
                alert('Upload error: ' + error.message);
            });

            fileFrame.on('select', function() {
                var attachment = fileFrame.state().get('selection').first().toJSON();

                // Verify it's a JSON file
                if (attachment.filename && !attachment.filename.toLowerCase().endsWith('.json')) {
                    alert('Please select a JSON file (.json extension)');
                    return;
                }

                $('#json-file-id').val(attachment.id);
                $('#selected-file-name').text(attachment.filename);
            });

            fileFrame.open();
        });

        // File selection for bulk import
        $('#select-multiple-files').on('click', function(e) {
            e.preventDefault();

            var fileFrame = wp.media({
                title: sibAdmin.i18n.selectFile,
                button: {
                    text: sibAdmin.i18n.selectFile
                },
                multiple: true,
                library: {
                    type: ['application/json', 'text/plain']  // Accept both JSON and text MIME types
                },
                uploader: {
                    allowedTypes: ['json']  // Only allow .json extension
                }
            });

            // Handle upload errors
            fileFrame.on('uploader:error', function(error) {
                alert('Upload error: ' + error.message);
            });

            fileFrame.on('select', function() {
                var attachments = fileFrame.state().get('selection').toJSON();
                var fileIds = [];
                var validFiles = true;

                attachments.forEach(function(attachment) {
                    // Verify it's a JSON file
                    if (attachment.filename && !attachment.filename.toLowerCase().endsWith('.json')) {
                        alert('All selected files must be JSON files (.json extension)');
                        validFiles = false;
                        return false;
                    }
                    fileIds.push(attachment.id);
                });

                if (!validFiles) {
                    return;
                }

                $('#json-files-ids').val(fileIds.join(','));
                $('#selected-files-count').text(attachments.length + ' files selected');
            });

            fileFrame.open();
        });

        // Browse directory
        $('#browse-directory').on('click', function(e) {
            e.preventDefault();
            // This is a placeholder - in a real implementation, you might use a server-side
            // directory browser or a custom dialog. For now, we'll just show an alert.
            alert('Directory browsing is not implemented in this demo. Please enter the directory path manually.');
        });

        // Import single JSON file from media library
        $('#import-json').on('click', function(e) {
            e.preventDefault();

            var fileId = $('#json-file-id').val();
            var debugMode = $('#debug-mode-single').is(':checked');

            if (!fileId) {
                alert(sibAdmin.i18n.noFileSelected);
                return;
            }

            var $button = $(this);
            var originalText = $button.text();

            $button.text(sibAdmin.i18n.importing).prop('disabled', true);

            $.ajax({
                url: sibAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'sib_import_json_by_id',
                    nonce: sibAdmin.nonce,
                    file_id: fileId,
                    debug_mode: debugMode
                },
                success: function(response) {
                    if (response.success) {
                        addResultMessage('success', response.data.message, response.data.debug);
                    } else {
                        addResultMessage('error', response.data.message, response.data.debug);
                    }
                },
                error: function(xhr, status, error) {
                    addResultMessage('error', sibAdmin.i18n.importFailed + ' ' + error);
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });

        // Upload and import JSON file directly
        $('#upload-and-import-json').on('click', function(e) {
            e.preventDefault();

            var fileInput = $('#direct-json-file')[0];
            var debugMode = $('#debug-mode-direct').is(':checked');

            if (!fileInput.files.length) {
                alert(sibAdmin.i18n.noFileSelected);
                return;
            }

            var file = fileInput.files[0];

            // Check if it's a JSON file
            if (!file.name.toLowerCase().endsWith('.json')) {
                alert('Please select a JSON file (.json extension)');
                return;
            }

            // Check file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('File is too large. Maximum size is 10MB.');
                return;
            }

            var $button = $(this);
            var originalText = $button.text();

            $button.text(sibAdmin.i18n.importing).prop('disabled', true);

            // Show a loading message in the results area
            addResultMessage('info', 'Uploading and processing file: ' + file.name + '...', 'File size: ' + (file.size / 1024).toFixed(2) + ' KB');

            // Create FormData object
            var formData = new FormData();
            formData.append('action', 'sib_upload_and_import_json');
            formData.append('nonce', sibAdmin.nonce);
            formData.append('json_file', file);
            formData.append('debug_mode', debugMode ? 'true' : 'false');

            $.ajax({
                url: sibAdmin.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 60000, // 60 second timeout
                success: function(response) {
                    if (response.success) {
                        addResultMessage('success', response.data.message, response.data.debug);

                        // Clear the file input
                        fileInput.value = '';
                    } else {
                        addResultMessage('error', response.data.message, response.data.debug);
                    }
                },
                error: function(xhr, status, error) {
                    var errorMessage = sibAdmin.i18n.importFailed + ' ' + error;

                    // Try to get more detailed error information
                    if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.data && response.data.message) {
                                errorMessage += ': ' + response.data.message;
                            }
                        } catch (e) {
                            // If we can't parse the JSON, include part of the response text
                            errorMessage += ': ' + xhr.responseText.substring(0, 100) + '...';
                        }
                    }

                    addResultMessage('error', errorMessage, 'Status: ' + status + '\nError: ' + error + '\nResponse Code: ' + xhr.status);
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });

        // Import multiple JSON files
        $('#import-multiple-json').on('click', function(e) {
            e.preventDefault();

            var fileIds = $('#json-files-ids').val();
            var debugMode = $('#debug-mode-bulk').is(':checked');

            if (!fileIds) {
                alert(sibAdmin.i18n.noFileSelected);
                return;
            }

            var $button = $(this);
            var originalText = $button.text();

            $button.text(sibAdmin.i18n.importing).prop('disabled', true);

            // Show progress bar
            $('#bulk-import-progress').show();

            var fileIdsArray = fileIds.split(',');
            var totalFiles = fileIdsArray.length;
            var importedCount = 0;
            var successCount = 0;
            var failedCount = 0;

            // Import files one by one
            importNextFile(fileIdsArray, 0, totalFiles, debugMode);

            function importNextFile(files, index, total, debug) {
                if (index >= total) {
                    // All files processed
                    $button.text(originalText).prop('disabled', false);
                    updateProgressBar(100);

                    // Add summary message
                    var summaryMessage = 'Import completed. ' + successCount + ' of ' + total + ' files imported successfully.';
                    addResultMessage(
                        successCount === total ? 'success' : 'warning',
                        summaryMessage
                    );

                    return;
                }

                var fileId = files[index];

                $.ajax({
                    url: sibAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'sib_import_json_by_id',
                        nonce: sibAdmin.nonce,
                        file_id: fileId,
                        debug_mode: debug
                    },
                    success: function(response) {
                        importedCount++;

                        if (response.success) {
                            successCount++;
                            addResultMessage('success', response.data.message, response.data.debug);
                        } else {
                            failedCount++;
                            addResultMessage('error', response.data.message, response.data.debug);
                        }

                        // Update progress
                        var progress = Math.round((importedCount / total) * 100);
                        updateProgressBar(progress);

                        // Import next file
                        importNextFile(files, index + 1, total, debug);
                    },
                    error: function(xhr, status, error) {
                        importedCount++;
                        failedCount++;

                        addResultMessage('error', sibAdmin.i18n.importFailed + ' ' + error);

                        // Update progress
                        var progress = Math.round((importedCount / total) * 100);
                        updateProgressBar(progress);

                        // Import next file
                        importNextFile(files, index + 1, total, debug);
                    }
                });
            }
        });

        // Import all JSON files from directory
        $('#import-all-json').on('click', function(e) {
            e.preventDefault();

            var directory = $('#json-directory').val();
            var debugMode = $('#debug-mode-advanced').is(':checked');

            if (!directory) {
                alert('Please enter a directory path');
                return;
            }

            if (!confirm(sibAdmin.i18n.confirmImportAll)) {
                return;
            }

            var $button = $(this);
            var originalText = $button.text();

            $button.text(sibAdmin.i18n.importing).prop('disabled', true);

            // Show progress bar
            $('#directory-import-progress').show();
            updateProgressBar(0, '#directory-import-progress');

            $.ajax({
                url: sibAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'sib_import_all_json',
                    nonce: sibAdmin.nonce,
                    directory: directory,
                    debug_mode: debugMode
                },
                success: function(response) {
                    if (response.success) {
                        addResultMessage('success', response.data.message);

                        // Update progress bar to 100%
                        updateProgressBar(100, '#directory-import-progress');

                        // Add individual results
                        if (response.data.results) {
                            if (response.data.results.imported && response.data.results.imported.length > 0) {
                                response.data.results.imported.forEach(function(item) {
                                    addResultMessage('success', 'Imported: ' + item.title, item.debug);
                                });
                            }

                            if (response.data.results.failed && response.data.results.failed.length > 0) {
                                response.data.results.failed.forEach(function(item) {
                                    addResultMessage('error', 'Failed to import: ' + item.file + ' - ' + item.error, item.debug);
                                });
                            }
                        }
                    } else {
                        addResultMessage('error', response.data.message, response.data.debug);
                        updateProgressBar(100, '#directory-import-progress');
                    }
                },
                error: function(xhr, status, error) {
                    addResultMessage('error', sibAdmin.i18n.importFailed + ' ' + error);
                    updateProgressBar(100, '#directory-import-progress');
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });

        // Import local JSON file from spadata directory
        $('#import-local-json').on('click', function(e) {
            e.preventDefault();

            var fileName = $('#local-json-file').val();
            var debugMode = $('#debug-mode-local').is(':checked');

            if (!fileName) {
                alert('Please enter a file name');
                return;
            }

            // Add .json extension if not present
            if (!fileName.toLowerCase().endsWith('.json')) {
                fileName += '.json';
            }

            var $button = $(this);
            var originalText = $button.text();

            $button.text(sibAdmin.i18n.importing).prop('disabled', true);

            // Show a loading message
            addResultMessage('info', 'Importing file from local directory: ' + fileName);

            $.ajax({
                url: sibAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'sib_import_json',
                    nonce: sibAdmin.nonce,
                    file_path: 'spadata/spas/' + fileName,
                    debug_mode: debugMode
                },
                success: function(response) {
                    if (response.success) {
                        addResultMessage('success', response.data.message, response.data.debug);
                    } else {
                        addResultMessage('error', response.data.message, response.data.debug);
                    }
                },
                error: function(xhr, status, error) {
                    addResultMessage('error', sibAdmin.i18n.importFailed + ' ' + error);
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });

        // Update progress bar
        function updateProgressBar(progress, selector) {
            selector = selector || '#bulk-import-progress';

            var $progressBar = $(selector + ' .sib-progress-bar');
            var $progressStatus = $(selector + ' .sib-progress-status');

            $progressBar.css('width', progress + '%');
            $progressStatus.text(progress + '%');
        }

        // Add result message to the results container
        function addResultMessage(type, message, debugInfo) {
            var $results = $('#import-results');
            var $message = $('<div class="sib-import-result"></div>');
            var icon, cssClass;

            switch (type) {
                case 'success':
                    icon = 'yes';
                    cssClass = 'sib-import-success';
                    break;
                case 'error':
                    icon = 'no';
                    cssClass = 'sib-import-error';
                    break;
                case 'warning':
                    icon = 'warning';
                    cssClass = 'sib-import-warning';
                    break;
                case 'info':
                    icon = 'info';
                    cssClass = 'sib-import-info';
                    break;
                default:
                    icon = 'info';
                    cssClass = '';
            }

            $message.addClass(cssClass).html('<span class="dashicons dashicons-' + icon + '"></span> ' + message);

            // Add debug information if available
            if (debugInfo) {
                var $debugToggle = $('<div class="sib-debug-toggle">' + sibAdmin.i18n.showDetails + '</div>');
                var $debugDetails = $('<div class="sib-debug-details"></div>').text(debugInfo);

                $debugToggle.on('click', function() {
                    var $details = $(this).next('.sib-debug-details');

                    if ($details.is(':visible')) {
                        $details.hide();
                        $(this).text(sibAdmin.i18n.showDetails);
                    } else {
                        $details.show();
                        $(this).text(sibAdmin.i18n.hideDetails);
                    }
                });

                $message.append($debugToggle).append($debugDetails);
            }

            // Clear "no results" message if it exists
            if ($results.find('p').length === 1 && $results.find('p').text() === 'Import results will appear here.') {
                $results.empty();
            }

            $results.prepend($message);

            // Scroll to the top of the results container
            $('html, body').animate({
                scrollTop: $results.offset().top - 50
            }, 500);
        }
    });

})(jQuery);
