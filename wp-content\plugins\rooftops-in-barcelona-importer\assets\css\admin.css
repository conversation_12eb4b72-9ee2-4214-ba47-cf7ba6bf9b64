/**
 * Admin styles for Spas in Barcelona Importer
 */

/* Import Page */
.sib-import-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 20px;
}

.sib-import-section {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.sib-import-form {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.sib-import-form:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.sib-form-row {
    margin-bottom: 15px;
}

.sib-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

/* Tabs */
.sib-import-tabs, .sib-help-tabs {
    margin-top: 15px;
}

.sib-tab-nav, .sib-help-tabs .sib-tab-nav, .sib-method-tabs {
    display: flex;
    border-bottom: 1px solid #ccc;
    margin-bottom: 20px;
}

.sib-tab-link, .sib-help-tab-link, .sib-method-tab {
    padding: 10px 15px;
    background: #f1f1f1;
    border: 1px solid #ccc;
    border-bottom: none;
    margin-right: 5px;
    cursor: pointer;
    font-weight: 500;
    border-radius: 3px 3px 0 0;
}

.sib-tab-link.active, .sib-help-tab-link.active, .sib-method-tab.active {
    background: #fff;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    font-weight: 600;
}

.sib-tab-content, .sib-help-tab-content, .sib-method-content {
    display: none;
    padding: 15px 0;
}

.sib-tab-content.active, .sib-help-tab-content.active, .sib-method-content.active {
    display: block;
}

/* Import Methods */
.sib-import-methods {
    margin-top: 15px;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
    background: #f9f9f9;
}

.sib-method-tabs {
    margin-top: 0;
    margin-bottom: 15px;
}

#direct-upload-form {
    margin-top: 10px;
}

#direct-json-file {
    padding: 10px;
    border: 1px dashed #ccc;
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
}

/* File Upload */
.sib-file-upload-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.sib-selected-file {
    margin-left: 10px;
    padding: 5px 10px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 3px;
    flex-grow: 1;
}

.sib-directory-input-container {
    display: flex;
    gap: 10px;
}

.sib-directory-input-container input {
    flex-grow: 1;
}

/* Progress Bar */
.sib-progress-container {
    margin: 15px 0;
    background: #f1f1f1;
    border-radius: 3px;
    position: relative;
    height: 25px;
    overflow: hidden;
}

.sib-progress-bar {
    background: #0073aa;
    height: 100%;
    width: 0;
    transition: width 0.3s ease;
}

.sib-progress-status {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    text-align: center;
    line-height: 25px;
    color: #fff;
    font-weight: 600;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

/* Debug Mode */
.sib-checkbox-label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.sib-checkbox-label input {
    margin-right: 8px;
}

/* Import Results */
.sib-import-results {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
}

.sib-import-result {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.sib-import-result:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.sib-import-success {
    color: #46b450;
}

.sib-import-error {
    color: #dc3232;
}

.sib-import-warning {
    color: #ffb900;
}

.sib-import-info {
    color: #0073aa;
}

.sib-debug-details {
    margin-top: 10px;
    padding: 10px;
    background: #f1f1f1;
    border-left: 3px solid #0073aa;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    display: none;
}

.sib-debug-toggle {
    margin-top: 5px;
    color: #0073aa;
    cursor: pointer;
    font-size: 12px;
    text-decoration: underline;
}

/* Help Section */
.sib-import-help {
    margin-top: 30px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.sib-import-help pre {
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
    overflow-x: auto;
}

.sib-troubleshooting-list {
    margin: 0;
    padding: 0;
}

.sib-troubleshooting-list li {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.sib-troubleshooting-list li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.sib-troubleshooting-list strong {
    display: block;
    margin-bottom: 5px;
}

/* Meta Boxes */
.sib-meta-box {
    margin: 0 -12px;
}

.sib-images-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.sib-image-item {
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    overflow: hidden;
}

.sib-image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
}

.sib-image-info {
    padding: 10px;
    background-color: #f9f9f9;
    font-size: 12px;
}

.sib-reviews-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.sib-review-item {
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
    background-color: #f9f9f9;
}

.sib-review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.sib-review-author {
    font-weight: 600;
}

.sib-review-rating {
    color: #ffb900;
}

.sib-review-content {
    margin-bottom: 10px;
    font-style: italic;
}

.sib-review-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #777;
}

/* Dashboard Widget */
.sib-dashboard-widget {
    margin: -12px;
}

.sib-stats {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #eee;
}

.sib-stat-item {
    flex: 1;
    min-width: 80px;
    text-align: center;
    padding: 15px 10px;
    border-right: 1px solid #eee;
}

.sib-stat-item:last-child {
    border-right: none;
}

.sib-stat-value {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #0073aa;
}

.sib-stat-label {
    display: block;
    font-size: 13px;
    color: #777;
}

.sib-recent-spas {
    padding: 0 15px;
    margin-bottom: 20px;
}

.sib-recent-spas h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.sib-recent-spas ul {
    margin: 0;
}

.sib-recent-spas li {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.sib-recent-spas li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.sib-spa-date {
    color: #777;
    font-size: 12px;
}

.sib-actions {
    padding: 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
}

/* Tools Page */
.sib-tools-container {
    margin-top: 20px;
}

.sib-tools-section {
    margin-bottom: 30px;
}

.sib-tool-card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 15px;
}

.sib-tool-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.sib-tool-card p {
    margin-bottom: 20px;
}

/* Welcome Notice */
.sib-welcome-notice {
    padding: 20px;
}

.sib-welcome-notice h3 {
    margin-top: 0;
}
