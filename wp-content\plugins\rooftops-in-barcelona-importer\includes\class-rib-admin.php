<?php
/**
 * Admin class
 *
 * Handles admin interface and functionality
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class RIB_Admin {
    /**
     * Constructor
     */
    public function __construct() {
        // Add admin menu
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

        // Register admin scripts and styles
        add_action( 'admin_enqueue_scripts', array( $this, 'register_admin_scripts' ) );

        // Add meta boxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );

        // Handle tag reimport action
        add_action( 'admin_init', array( $this, 'handle_tag_reimport' ) );

        // Add dashboard widget
        add_action( 'wp_dashboard_setup', array( $this, 'add_dashboard_widget' ) );

        // Add admin notices
        add_action( 'admin_notices', array( $this, 'admin_notices' ) );
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=rooftop',
            __( 'Import Rooftops', 'rooftops-in-barcelona-importer' ),
            __( 'Import Rooftops', 'rooftops-in-barcelona-importer' ),
            'manage_options',
            'rib-import',
            array( $this, 'render_import_page' )
        );

        add_submenu_page(
            'edit.php?post_type=rooftop',
            __( 'Rooftop Tools', 'rooftops-in-barcelona-importer' ),
            __( 'Rooftop Tools', 'rooftops-in-barcelona-importer' ),
            'manage_options',
            'rib-tools',
            array( $this, 'render_tools_page' )
        );
    }

    /**
     * Register admin scripts and styles
     */
    public function register_admin_scripts( $hook ) {
        // Only load on our admin page or rooftop edit pages
        if ( 'rooftop_page_rib-import' !== $hook && 'post.php' !== $hook && 'post-new.php' !== $hook ) {
            return;
        }

        // Register and enqueue admin styles
        wp_register_style(
            'rib-admin-styles',
            RIB_IMPORTER_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            RIB_IMPORTER_VERSION
        );
        wp_enqueue_style( 'rib-admin-styles' );

        // Enqueue WordPress media scripts
        wp_enqueue_media();

        // Register and enqueue admin scripts
        wp_register_script(
            'rib-admin-scripts',
            RIB_IMPORTER_PLUGIN_URL . 'assets/js/admin.js',
            array( 'jquery', 'jquery-ui-progressbar' ),
            RIB_IMPORTER_VERSION,
            true
        );

        // Localize script
        wp_localize_script(
            'rib-admin-scripts',
            'ribAdmin',
            array(
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'nonce'   => wp_create_nonce( 'rib_import_nonce' ),
                'i18n'    => array(
                    'importing'        => __( 'Importing...', 'rooftops-in-barcelona-importer' ),
                    'importComplete'   => __( 'Import complete!', 'rooftops-in-barcelona-importer' ),
                    'importFailed'     => __( 'Import failed:', 'rooftops-in-barcelona-importer' ),
                    'confirmImportAll' => __( 'Are you sure you want to import all JSON files? This may take a while.', 'rooftops-in-barcelona-importer' ),
                    'selectFile'       => __( 'Select JSON File', 'rooftops-in-barcelona-importer' ),
                    'selectDirectory'  => __( 'Select Directory', 'rooftops-in-barcelona-importer' ),
                    'noFileSelected'   => __( 'No file selected', 'rooftops-in-barcelona-importer' ),
                    'processingFile'   => __( 'Processing file:', 'rooftops-in-barcelona-importer' ),
                    'debugDetails'     => __( 'Debug Details', 'rooftops-in-barcelona-importer' ),
                    'showDetails'      => __( 'Show Details', 'rooftops-in-barcelona-importer' ),
                    'hideDetails'      => __( 'Hide Details', 'rooftops-in-barcelona-importer' ),
                ),
            )
        );

        // Add jQuery UI styles
        wp_enqueue_style('jquery-ui-progressbar', 'https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css');

        wp_enqueue_script( 'rib-admin-scripts' );
    }

    /**
     * Render import page
     */
    public function render_import_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Import Rooftops', 'rooftops-in-barcelona-importer' ); ?></h1>

            <div class="rib-import-container">
                <div class="rib-import-section">
                    <h2><?php _e( 'Import from JSON Files', 'rooftops-in-barcelona-importer' ); ?></h2>
                    <p><?php _e( 'Import rooftop data from JSON files.', 'rooftops-in-barcelona-importer' ); ?></p>

                    <div class="rib-import-tabs">
                        <div class="rib-tab-nav">
                            <button class="rib-tab-link active" data-tab="single-import"><?php _e( 'Single Import', 'rooftops-in-barcelona-importer' ); ?></button>
                            <button class="rib-tab-link" data-tab="bulk-import"><?php _e( 'Bulk Import', 'rooftops-in-barcelona-importer' ); ?></button>
                            <button class="rib-tab-link" data-tab="advanced-import"><?php _e( 'Advanced', 'rooftops-in-barcelona-importer' ); ?></button>
                        </div>

                        <!-- Single Import Tab -->
                        <div id="single-import" class="rib-tab-content active">
                            <div class="rib-import-form">
                                <h3><?php _e( 'Import Single JSON File', 'rooftops-in-barcelona-importer' ); ?></h3>
                                <p><?php _e( 'Select a JSON file to import a single rooftop.', 'rooftops-in-barcelona-importer' ); ?></p>

                                <div class="rib-import-methods">
                                    <div class="rib-method-tabs">
                                        <button class="rib-method-tab active" data-method="media-library"><?php _e( 'Media Library', 'rooftops-in-barcelona-importer' ); ?></button>
                                        <button class="rib-method-tab" data-method="direct-upload"><?php _e( 'Direct Upload', 'rooftops-in-barcelona-importer' ); ?></button>
                                    </div>

                                    <!-- Media Library Method -->
                                    <div id="media-library-method" class="rib-method-content active">
                                        <div class="rib-form-row">
                                            <div class="rib-file-upload-container">
                                                <button id="select-json-file" class="button"><?php _e( 'Select File', 'rooftops-in-barcelona-importer' ); ?></button>
                                                <div id="selected-file-name" class="rib-selected-file"><?php _e( 'No file selected', 'rooftops-in-barcelona-importer' ); ?></div>
                                                <input type="hidden" id="json-file-id" value="" />
                                            </div>
                                        </div>

                                        <div class="rib-form-row">
                                            <label class="rib-checkbox-label">
                                                <input type="checkbox" id="debug-mode-single" class="debug-mode-toggle">
                                                <?php _e( 'Enable Debug Mode', 'rooftops-in-barcelona-importer' ); ?>
                                            </label>
                                        </div>

                                        <div class="rib-form-row">
                                            <button id="import-json" class="button button-primary"><?php _e( 'Import JSON File', 'rooftops-in-barcelona-importer' ); ?></button>
                                        </div>
                                    </div>

                                    <!-- Direct Upload Method -->
                                    <div id="direct-upload-method" class="rib-method-content">
                                        <form id="direct-upload-form" enctype="multipart/form-data">
                                            <div class="rib-form-row">
                                                <label for="direct-json-file"><?php _e( 'Choose JSON File:', 'rooftops-in-barcelona-importer' ); ?></label>
                                                <input type="file" id="direct-json-file" name="json_file" accept=".json" required />
                                            </div>

                                            <div class="rib-form-row">
                                                <label class="rib-checkbox-label">
                                                    <input type="checkbox" id="debug-mode-direct" class="debug-mode-toggle">
                                                    <?php _e( 'Enable Debug Mode', 'rooftops-in-barcelona-importer' ); ?>
                                                </label>
                                            </div>

                                            <div class="rib-form-row">
                                                <button id="upload-and-import-json" class="button button-primary"><?php _e( 'Upload & Import', 'rooftops-in-barcelona-importer' ); ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Import Tab -->
                        <div id="bulk-import" class="rib-tab-content">
                            <div class="rib-import-form">
                                <h3><?php _e( 'Import Multiple JSON Files', 'rooftops-in-barcelona-importer' ); ?></h3>
                                <p><?php _e( 'Select multiple JSON files to import several rooftops at once.', 'rooftops-in-barcelona-importer' ); ?></p>

                                <div class="rib-form-row">
                                    <div class="rib-file-upload-container">
                                        <button id="select-multiple-files" class="button"><?php _e( 'Select Files', 'rooftops-in-barcelona-importer' ); ?></button>
                                        <div id="selected-files-count" class="rib-selected-file"><?php _e( 'No files selected', 'rooftops-in-barcelona-importer' ); ?></div>
                                        <input type="hidden" id="json-files-ids" value="" />
                                    </div>
                                </div>

                                <div class="rib-form-row">
                                    <label class="rib-checkbox-label">
                                        <input type="checkbox" id="debug-mode-bulk" class="debug-mode-toggle">
                                        <?php _e( 'Enable Debug Mode', 'rooftops-in-barcelona-importer' ); ?>
                                    </label>
                                </div>

                                <div id="bulk-import-progress" class="rib-progress-container" style="display: none;">
                                    <div class="rib-progress-bar"></div>
                                    <div class="rib-progress-status">0%</div>
                                </div>

                                <div class="rib-form-row">
                                    <button id="import-multiple-json" class="button button-primary"><?php _e( 'Import Selected Files', 'rooftops-in-barcelona-importer' ); ?></button>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Import Tab -->
                        <div id="advanced-import" class="rib-tab-content">
                            <div class="rib-import-form">
                                <h3><?php _e( 'Import All JSON Files from Directory', 'rooftops-in-barcelona-importer' ); ?></h3>
                                <p><?php _e( 'Import all JSON files from a specific directory on the server.', 'rooftops-in-barcelona-importer' ); ?></p>

                                <div class="rib-form-row">
                                    <label for="json-directory"><?php _e( 'Directory Path:', 'rooftops-in-barcelona-importer' ); ?></label>
                                    <div class="rib-directory-input-container">
                                        <input type="text" id="json-directory" class="regular-text" value="<?php echo esc_attr( WP_CONTENT_DIR . '/uploads/2025/04' ); ?>" />
                                        <button id="browse-directory" class="button"><?php _e( 'Browse', 'rooftops-in-barcelona-importer' ); ?></button>
                                    </div>
                                </div>

                                <div class="rib-form-row">
                                    <label class="rib-checkbox-label">
                                        <input type="checkbox" id="debug-mode-advanced" class="debug-mode-toggle">
                                        <?php _e( 'Enable Debug Mode', 'rooftops-in-barcelona-importer' ); ?>
                                    </label>
                                </div>

                                <div id="directory-import-progress" class="rib-progress-container" style="display: none;">
                                    <div class="rib-progress-bar"></div>
                                    <div class="rib-progress-status">0%</div>
                                </div>

                                <div class="rib-form-row">
                                    <button id="import-all-json" class="button button-primary"><?php _e( 'Import All JSON Files', 'rooftops-in-barcelona-importer' ); ?></button>
                                </div>
                            </div>

                            <div class="rib-import-form">
                                <h3><?php _e( 'Import from Local Spa Data Directory', 'rooftops-in-barcelona-importer' ); ?></h3>
                                <p><?php _e( 'Import a specific JSON file from the spadata/spas directory.', 'rooftops-in-barcelona-importer' ); ?></p>

                                <div class="rib-form-row">
                                    <label for="local-json-file"><?php _e( 'File Name:', 'rooftops-in-barcelona-importer' ); ?></label>
                                    <div class="rib-directory-input-container">
                                        <input type="text" id="local-json-file" class="regular-text" placeholder="the_spa_at_mandarin_oriental_barcelona.json" />
                                    </div>
                                    <p class="description"><?php _e( 'Enter the filename only, not the full path. The file should be in the spadata/spas directory.', 'rooftops-in-barcelona-importer' ); ?></p>
                                </div>

                                <div class="rib-form-row">
                                    <label class="rib-checkbox-label">
                                        <input type="checkbox" id="debug-mode-local" class="debug-mode-toggle">
                                        <?php _e( 'Enable Debug Mode', 'rooftops-in-barcelona-importer' ); ?>
                                    </label>
                                </div>

                                <div class="rib-form-row">
                                    <button id="import-local-json" class="button button-primary"><?php _e( 'Import Local File', 'rooftops-in-barcelona-importer' ); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="rib-import-section">
                    <h2><?php _e( 'Import Results', 'rooftops-in-barcelona-importer' ); ?></h2>
                    <div id="import-results" class="rib-import-results">
                        <p><?php _e( 'Import results will appear here.', 'rooftops-in-barcelona-importer' ); ?></p>
                    </div>
                </div>
            </div>

            <div class="rib-import-help">
                <h2><?php _e( 'Help & Documentation', 'rooftops-in-barcelona-importer' ); ?></h2>
                <div class="rib-help-tabs">
                    <div class="rib-tab-nav">
                        <button class="rib-help-tab-link active" data-tab="json-structure"><?php _e( 'JSON Structure', 'rooftops-in-barcelona-importer' ); ?></button>
                        <button class="rib-help-tab-link" data-tab="troubleshooting"><?php _e( 'Troubleshooting', 'rooftops-in-barcelona-importer' ); ?></button>
                    </div>

                    <div id="json-structure" class="rib-help-tab-content active">
                        <p><?php _e( 'The JSON files should have the following structure:', 'rooftops-in-barcelona-importer' ); ?></p>
                        <pre>
{
  "name": "Spa Name",
  "location": {
    "address": "Street Address",
    "city": "Barcelona",
    "country": "Spain",
    "neighborhood": "Vila Olímpica",
    "coordinates": {
      "latitude": 41.3851,
      "longitude": 2.1734
    }
  },
  "contact": {
    "phone": "+34 123 456 789",
    "email": "<EMAIL>",
    "website": "https://example.com"
  },
  "services": [
    "Massage",
    "Facial Treatments",
    "Body Treatments"
  ],
  ...
}
                        </pre>
                    </div>

                    <div id="troubleshooting" class="rib-help-tab-content">
                        <h3><?php _e( 'Common Import Issues', 'rooftops-in-barcelona-importer' ); ?></h3>
                        <ul class="rib-troubleshooting-list">
                            <li>
                                <strong><?php _e( 'Invalid JSON Format', 'rooftops-in-barcelona-importer' ); ?></strong>
                                <p><?php _e( 'Make sure your JSON file is properly formatted. You can validate it using online tools like JSONLint.', 'rooftops-in-barcelona-importer' ); ?></p>
                            </li>
                            <li>
                                <strong><?php _e( 'Missing Required Fields', 'rooftops-in-barcelona-importer' ); ?></strong>
                                <p><?php _e( 'The "name" field is required for all spa entries. Make sure it exists in your JSON file.', 'rooftops-in-barcelona-importer' ); ?></p>
                            </li>
                            <li>
                                <strong><?php _e( 'File Permissions', 'rooftops-in-barcelona-importer' ); ?></strong>
                                <p><?php _e( 'If importing from a directory, make sure the web server has read permissions for the files.', 'rooftops-in-barcelona-importer' ); ?></p>
                            </li>
                            <li>
                                <strong><?php _e( 'Debug Mode', 'rooftops-in-barcelona-importer' ); ?></strong>
                                <p><?php _e( 'Enable Debug Mode to see detailed error messages and JSON parsing information.', 'rooftops-in-barcelona-importer' ); ?></p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'sib_spa_images',
            __( 'Rooftop Images', 'rooftops-in-barcelona-importer' ),
            array( $this, 'render_images_meta_box' ),
            'rooftop',
            'normal',
            'high'
        );

        add_meta_box(
            'sib_spa_reviews',
            __( 'Rooftop Reviews', 'rooftops-in-barcelona-importer' ),
            array( $this, 'render_reviews_meta_box' ),
            'rooftop',
            'normal',
            'high'
        );

        add_meta_box(
            'sib_spa_packages',
            __( 'Rooftop Packages', 'rooftops-in-barcelona-importer' ),
            array( $this, 'render_packages_meta_box' ),
            'rooftop',
            'normal',
            'default'
        );
    }

    /**
     * Render images meta box
     */
    public function render_images_meta_box( $post ) {
        $images = get_post_meta( $post->ID, 'images', true );
        ?>
        <div class="rib-meta-box">
            <div class="rib-images-container">
                <?php if ( ! empty( $images ) && is_array( $images ) ) : ?>
                    <?php foreach ( $images as $index => $image ) : ?>
                        <?php if ( ! empty( $image['url'] ) ) : ?>
                            <div class="rib-image-item">
                                <img src="<?php echo esc_url( $image['url'] ); ?>" alt="<?php echo esc_attr( ! empty( $image['alt'] ) ? $image['alt'] : '' ); ?>">
                                <div class="rib-image-info">
                                    <?php if ( ! empty( $image['caption'] ) ) : ?>
                                        <p><strong><?php _e( 'Caption:', 'rooftops-in-barcelona-importer' ); ?></strong> <?php echo esc_html( $image['caption'] ); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php else : ?>
                    <p><?php _e( 'No images available.', 'rooftops-in-barcelona-importer' ); ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render reviews meta box
     */
    public function render_reviews_meta_box( $post ) {
        $reviews = get_post_meta( $post->ID, 'reviews', true );
        ?>
        <div class="rib-meta-box">
            <?php if ( ! empty( $reviews ) ) : ?>
                <?php if ( ! empty( $reviews['review_sources'] ) && is_array( $reviews['review_sources'] ) ) : ?>
                    <h4><?php _e( 'Review Sources', 'rooftops-in-barcelona-importer' ); ?></h4>
                    <table class="widefat">
                        <thead>
                            <tr>
                                <th><?php _e( 'Source', 'rooftops-in-barcelona-importer' ); ?></th>
                                <th><?php _e( 'Rating', 'rooftops-in-barcelona-importer' ); ?></th>
                                <th><?php _e( 'Count', 'rooftops-in-barcelona-importer' ); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ( $reviews['review_sources'] as $source ) : ?>
                                <tr>
                                    <td><?php echo esc_html( $source['source'] ); ?></td>
                                    <td><?php echo esc_html( $source['rating'] ); ?>/5</td>
                                    <td><?php echo esc_html( $source['count'] ); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>

                <?php if ( ! empty( $reviews['featured_reviews'] ) && is_array( $reviews['featured_reviews'] ) ) : ?>
                    <h4><?php _e( 'Featured Reviews', 'rooftops-in-barcelona-importer' ); ?></h4>
                    <div class="rib-reviews-container">
                        <?php foreach ( $reviews['featured_reviews'] as $review ) : ?>
                            <div class="rib-review-item">
                                <div class="rib-review-header">
                                    <span class="rib-review-author"><?php echo esc_html( $review['author'] ); ?></span>
                                    <span class="rib-review-rating"><?php echo esc_html( $review['rating'] ); ?>/5</span>
                                </div>
                                <div class="rib-review-content">
                                    <?php echo esc_html( $review['text'] ); ?>
                                </div>
                                <div class="rib-review-meta">
                                    <?php if ( ! empty( $review['date'] ) ) : ?>
                                        <span class="rib-review-date"><?php echo esc_html( $review['date'] ); ?></span>
                                    <?php endif; ?>
                                    <?php if ( ! empty( $review['source'] ) ) : ?>
                                        <span class="rib-review-source"><?php echo esc_html( $review['source'] ); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php else : ?>
                <p><?php _e( 'No reviews available.', 'rooftops-in-barcelona-importer' ); ?></p>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Render packages meta box
     */
    public function render_packages_meta_box( $post ) {
        $packages = get_post_meta( $post->ID, 'packages', true );
        ?>
        <div class="rib-meta-box">
            <?php if ( ! empty( $packages ) && is_array( $packages ) ) : ?>
                <table class="widefat">
                    <thead>
                        <tr>
                            <th><?php _e( 'Name', 'rooftops-in-barcelona-importer' ); ?></th>
                            <th><?php _e( 'Description', 'rooftops-in-barcelona-importer' ); ?></th>
                            <th><?php _e( 'Price', 'rooftops-in-barcelona-importer' ); ?></th>
                            <th><?php _e( 'Duration', 'rooftops-in-barcelona-importer' ); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ( $packages as $package ) : ?>
                            <tr>
                                <td><?php echo esc_html( $package['name'] ); ?></td>
                                <td><?php echo esc_html( ! empty( $package['description'] ) ? $package['description'] : '' ); ?></td>
                                <td><?php echo esc_html( ! empty( $package['price'] ) ? $package['price'] : '' ); ?></td>
                                <td><?php echo esc_html( ! empty( $package['duration'] ) ? $package['duration'] : '' ); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <p><?php _e( 'No packages available.', 'rooftops-in-barcelona-importer' ); ?></p>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Add dashboard widget
     */
    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'sib_dashboard_widget',
            __( 'Spas in Barcelona', 'rooftops-in-barcelona-importer' ),
            array( $this, 'render_dashboard_widget' )
        );
    }

    /**
     * Render dashboard widget
     */
    public function render_dashboard_widget() {
        // Get spa stats
        $total_spas = wp_count_posts( 'rooftop' )->publish;
        $categories = wp_count_terms( 'spa_category' );
        $services = wp_count_terms( 'spa_service' );
        $features = wp_count_terms( 'spa_feature' );

        // Get recent spas
        $recent_spas = get_posts( array(
            'post_type'      => 'rooftop',
            'posts_per_page' => 5,
            'orderby'        => 'date',
            'order'          => 'DESC',
        ) );
        ?>
        <div class="rib-dashboard-widget">
            <div class="rib-stats">
                <div class="rib-stat-item">
                    <span class="rib-stat-value"><?php echo esc_html( $total_spas ); ?></span>
                    <span class="rib-stat-label"><?php _e( 'Spas', 'rooftops-in-barcelona-importer' ); ?></span>
                </div>
                <div class="rib-stat-item">
                    <span class="rib-stat-value"><?php echo esc_html( $categories ); ?></span>
                    <span class="rib-stat-label"><?php _e( 'Categories', 'rooftops-in-barcelona-importer' ); ?></span>
                </div>
                <div class="rib-stat-item">
                    <span class="rib-stat-value"><?php echo esc_html( $services ); ?></span>
                    <span class="rib-stat-label"><?php _e( 'Services', 'rooftops-in-barcelona-importer' ); ?></span>
                </div>
                <div class="rib-stat-item">
                    <span class="rib-stat-value"><?php echo esc_html( $features ); ?></span>
                    <span class="rib-stat-label"><?php _e( 'Features', 'rooftops-in-barcelona-importer' ); ?></span>
                </div>
            </div>

            <?php if ( ! empty( $recent_spas ) ) : ?>
                <div class="rib-recent-spas">
                    <h3><?php _e( 'Recently Added Spas', 'rooftops-in-barcelona-importer' ); ?></h3>
                    <ul>
                        <?php foreach ( $recent_spas as $spa ) : ?>
                            <li>
                                <a href="<?php echo esc_url( get_edit_post_link( $spa->ID ) ); ?>">
                                    <?php echo esc_html( $spa->post_title ); ?>
                                </a>
                                <span class="rib-spa-date"><?php echo esc_html( get_the_date( '', $spa->ID ) ); ?></span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="rib-actions">
                <a href="<?php echo esc_url( admin_url( 'post-new.php?post_type=spa' ) ); ?>" class="button button-primary">
                    <?php _e( 'Add New Spa', 'rooftops-in-barcelona-importer' ); ?>
                </a>
                <a href="<?php echo esc_url( admin_url( 'edit.php?post_type=spa&page=rib-import' ) ); ?>" class="button">
                    <?php _e( 'Import Rooftops', 'rooftops-in-barcelona-importer' ); ?>
                </a>
            </div>
        </div>
        <?php
    }

    /**
     * Render tools page
     */
    public function render_tools_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Spa Tools', 'rooftops-in-barcelona-importer' ); ?></h1>

            <div class="rib-tools-container">
                <div class="rib-tools-section">
                    <h2><?php _e( 'Tag Management', 'rooftops-in-barcelona-importer' ); ?></h2>
                    <p><?php _e( 'Use these tools to manage tags for spa posts.', 'rooftops-in-barcelona-importer' ); ?></p>

                    <div class="rib-tool-card">
                        <h3><?php _e( 'Re-import Tags', 'rooftops-in-barcelona-importer' ); ?></h3>
                        <p><?php _e( 'This tool will re-import tags from post meta to the WordPress tag system for all spa posts. Use this if your tags are not showing up correctly.', 'rooftops-in-barcelona-importer' ); ?></p>

                        <form method="post" action="">
                            <?php wp_nonce_field( 'sib_reimport_tags', 'sib_reimport_tags_nonce' ); ?>
                            <input type="hidden" name="sib_action" value="reimport_tags">
                            <button type="submit" class="button button-primary"><?php _e( 'Re-import All Tags', 'rooftops-in-barcelona-importer' ); ?></button>
                        </form>
                    </div>

                    <div class="rib-tool-card">
                        <h3><?php _e( 'Fix Popular Tags Display', 'rooftops-in-barcelona-importer' ); ?></h3>
                        <p><?php _e( 'This tool will fix the display of popular tags in spa pages. Use this if your tags are not showing up in the spa page header.', 'rooftops-in-barcelona-importer' ); ?></p>

                        <form method="post" action="">
                            <?php wp_nonce_field( 'sib_fix_popular_tags', 'sib_fix_popular_tags_nonce' ); ?>
                            <input type="hidden" name="sib_action" value="fix_popular_tags">
                            <button type="submit" class="button button-primary"><?php _e( 'Fix Popular Tags Display', 'rooftops-in-barcelona-importer' ); ?></button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Handle tag reimport
     */
    public function handle_tag_reimport() {
        // Check if we're on the right page and the right action is being performed
        if ( ! isset( $_POST['sib_action'] ) ) {
            return;
        }

        // Handle different actions
        if ( $_POST['sib_action'] === 'reimport_tags' ) {
            $this->process_reimport_tags();
        } elseif ( $_POST['sib_action'] === 'fix_popular_tags' ) {
            $this->process_fix_popular_tags();
        }
    }

    /**
     * Process reimport tags action
     */
    private function process_reimport_tags() {
        // Verify nonce
        if ( ! isset( $_POST['sib_reimport_tags_nonce'] ) || ! wp_verify_nonce( $_POST['sib_reimport_tags_nonce'], 'sib_reimport_tags' ) ) {
            wp_die( __( 'Security check failed', 'rooftops-in-barcelona-importer' ) );
        }

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'You do not have permission to perform this action', 'rooftops-in-barcelona-importer' ) );
        }

        // Get the importer class
        $importer = new SIB_Importer();

        // Re-import tags
        $updated_count = $importer->reimport_tags_for_all_spas();

        // Set a transient to show the success message
        set_transient( 'sib_tags_reimported', $updated_count, 60 );

        // Redirect back to the tools page
        wp_redirect( admin_url( 'edit.php?post_type=spa&page=rib-tools&tags_reimported=1' ) );
        exit;
    }

    /**
     * Process fix popular tags action
     */
    private function process_fix_popular_tags() {
        // Verify nonce
        if ( ! isset( $_POST['sib_fix_popular_tags_nonce'] ) || ! wp_verify_nonce( $_POST['sib_fix_popular_tags_nonce'], 'sib_fix_popular_tags' ) ) {
            wp_die( __( 'Security check failed', 'rooftops-in-barcelona-importer' ) );
        }

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'You do not have permission to perform this action', 'rooftops-in-barcelona-importer' ) );
        }

        // Get all spa posts
        $spa_posts = get_posts(array(
            'post_type' => 'rooftop',
            'posts_per_page' => -1,
            'post_status' => 'publish',
        ));

        $updated_count = 0;

        foreach ($spa_posts as $spa_post) {
            // Get popular features from post meta
            $popular = get_post_meta($spa_post->ID, 'popular', true);

            // If popular exists, make sure it's also saved as tags
            if (!empty($popular) && is_array($popular)) {
                // Update the tags meta field to match popular
                update_post_meta($spa_post->ID, 'tags', $popular);
                $updated_count++;
            }
        }

        // Set a transient to show the success message
        set_transient( 'sib_popular_tags_fixed', $updated_count, 60 );

        // Redirect back to the tools page
        wp_redirect( admin_url( 'edit.php?post_type=spa&page=rib-tools&popular_tags_fixed=1' ) );
        exit;
    }

    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check if tags were reimported
        $tags_reimported = get_transient( 'sib_tags_reimported' );
        if ( $tags_reimported !== false ) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><?php printf( __( 'Tags reimported successfully! %d spa posts were updated.', 'rooftops-in-barcelona-importer' ), $tags_reimported ); ?></p>
            </div>
            <?php
            delete_transient( 'sib_tags_reimported' );
        }

        // Check if popular tags were fixed
        $popular_tags_fixed = get_transient( 'sib_popular_tags_fixed' );
        if ( $popular_tags_fixed !== false ) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><?php printf( __( 'Popular tags display fixed successfully! %d spa posts were updated.', 'rooftops-in-barcelona-importer' ), $popular_tags_fixed ); ?></p>
            </div>
            <?php
            delete_transient( 'sib_popular_tags_fixed' );
        }

        // Check if we need to show the welcome notice
        if ( ! get_option( 'sib_welcome_notice_dismissed' ) ) {
            ?>
            <div class="notice notice-info is-dismissible rib-welcome-notice">
                <h3><?php _e( 'Welcome to Spas in Barcelona Importer!', 'rooftops-in-barcelona-importer' ); ?></h3>
                <p><?php _e( 'Thank you for installing the Spas in Barcelona Importer plugin. To get started, go to the Import Rooftops page to import your spa data.', 'rooftops-in-barcelona-importer' ); ?></p>
                <p>
                    <a href="<?php echo esc_url( admin_url( 'edit.php?post_type=spa&page=rib-import' ) ); ?>" class="button button-primary">
                        <?php _e( 'Import Rooftops', 'rooftops-in-barcelona-importer' ); ?>
                    </a>
                    <a href="#" class="button rib-dismiss-welcome">
                        <?php _e( 'Dismiss Notice', 'rooftops-in-barcelona-importer' ); ?>
                    </a>
                </p>
            </div>
            <script>
                jQuery(document).ready(function($) {
                    $('.rib-dismiss-welcome').on('click', function(e) {
                        e.preventDefault();

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'sib_dismiss_welcome_notice',
                                nonce: '<?php echo wp_create_nonce( 'sib_dismiss_notice' ); ?>'
                            }
                        });

                        $(this).closest('.notice').fadeOut();
                    });
                });
            </script>
            <?php
        }
    }
}
